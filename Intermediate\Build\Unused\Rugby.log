﻿  *** Found installed NintendoSDK version 19.3.5.  That version is greater than the maximum suggested version of 16.2.6 and is unverified. ***
  Building RugbyEditor and ShaderCompileWorker...
  Using Visual Studio 2019 14.29.30159 toolchain (C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133) and Windows 10.0.22000.0 SDK (C:\Program Files (x86)\Windows Kits\10).
  [Upgrade]
  [Upgrade] Using backward-compatible build settings. The latest version of UE4 sets the following values by default, which may require code changes:
  [Upgrade]     bLegacyPublicIncludePaths = false                 => Omits subfolders from public include paths to reduce compiler command line length. (Previously: true).
  [Upgrade]     ShadowVariableWarningLevel = WarningLevel.Error   => Treats shadowed variable warnings as errors. (Previously: WarningLevel.Warning).
  [Upgrade]     PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs   => Set in build.cs files to enables IWYU-style PCH model. See https://docs.unrealengine.com/en-US/Programming/BuildTools/UnrealBuildTool/IWYU/index.html. (Previously: PCHUsageMode.UseSharedPCHs).
  [Upgrade] Suppress this message by setting 'DefaultBuildSettings = BuildSettingsVersion.V2;' in RugbyEditor.Target.cs, and explicitly overriding settings that differ from the new defaults.
  [Upgrade]
  Building 4 actions with 24 processes...
    [1/4] Module.Rugby.19_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(872): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(899): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(1000): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(1387): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(1393): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(1802): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(3541): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(4106): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(4109): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(4118): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(4133): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/Input/SSInputManager.cpp(205): warning C4996: 'UEnum::GetEnumName': GetEnumName is deprecated, call GetNameStringByIndex instead Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [2/4] UE4Editor-Rugby.lib
       Creating library W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.lib and object W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.exp
    [3/4] UE4Editor-Rugby.dll
       Creating library W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.suppressed.lib and object W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.suppressed.exp
    [4/4] RugbyEditor.target
  Total time in Parallel executor: 24.62 seconds
  Total execution time: 25.72 seconds
